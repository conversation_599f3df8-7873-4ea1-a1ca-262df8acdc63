#!/usr/bin/env python3
"""
简单测试图例位置修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_legend_code():
    """测试图例相关代码修改"""
    print("=== 检查图例位置修改 ===")
    
    try:
        # 读取修改后的文件内容
        visualizer_file = current_dir / "analysis" / "historical_chart_visualizer.py"
        
        if not visualizer_file.exists():
            print("❌ 找不到历史图表可视化器文件")
            return False
        
        with open(visualizer_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("bbox_to_anchor=(1.05, 1)", "图例位置设置为图表外部"),
            ("loc='upper left'", "图例锚点设置为左上角"),
            ("subplots_adjust(right=0.85)", "为图例调整子图右边距"),
            ("rect=[0, 0, 0.85, 1]", "tight_layout为图例留出空间")
        ]
        
        all_found = True
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}: 找到 '{check_text}'")
            else:
                print(f"❌ {description}: 未找到 '{check_text}'")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        return False

def test_import():
    """测试模块导入"""
    print("\n=== 测试模块导入 ===")
    
    try:
        from analysis.historical_chart_visualizer import HistoricalChartVisualizer
        print("✅ 成功导入 HistoricalChartVisualizer")
        
        # 创建实例
        visualizer = HistoricalChartVisualizer()
        print("✅ 成功创建可视化器实例")
        
        # 检查_setup_legend方法是否存在
        if hasattr(visualizer, '_setup_legend'):
            print("✅ _setup_legend 方法存在")
            return True
        else:
            print("❌ _setup_legend 方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 导入测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始检查图例位置修改...")
    
    results = []
    
    # 运行测试
    results.append(("代码修改检查", test_legend_code()))
    results.append(("模块导入测试", test_import()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("检查结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有检查通过！")
        print("✅ 图例位置已成功修改")
        print("✅ 图例现在位于图表右侧外部")
        print("✅ 不再遮挡平局线的数据点")
        print("\n📋 修改说明:")
        print("1. 图例位置从 'upper right' 改为 bbox_to_anchor=(1.05, 1)")
        print("2. 图例锚点设置为 'upper left'")
        print("3. 调整子图右边距为 0.85，为图例留出空间")
        print("4. tight_layout 使用 rect=[0, 0, 0.85, 1] 参数")
    else:
        print("⚠️  部分检查失败，请检查修改。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
