#!/usr/bin/env python3
"""
测试历史区间标签页是否还会创建硬编码目录
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_historical_tab_directory_creation():
    """测试历史区间标签页的目录创建行为"""
    print("=== 测试历史区间标签页目录创建 ===")
    
    try:
        # 创建临时目录模拟新环境
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"临时测试目录: {temp_dir}")
            
            # 复制必要的文件到临时目录
            temp_project = Path(temp_dir) / "football_analysis_system"
            temp_project.mkdir()
            
            # 复制核心模块
            core_src = current_dir / "core"
            core_dst = temp_project / "core"
            if core_src.exists():
                shutil.copytree(core_src, core_dst)
            
            # 复制配置
            config_src = current_dir / "config"
            config_dst = temp_project / "config"
            if config_src.exists():
                shutil.copytree(config_src, config_dst)
            
            # 复制config.py
            config_py_src = current_dir / "config.py"
            config_py_dst = temp_project / "config.py"
            if config_py_src.exists():
                shutil.copy2(config_py_src, config_py_dst)
            
            # 创建__init__.py文件
            (temp_project / "__init__.py").touch()
            (temp_project / "core" / "__init__.py").touch()
            
            # 切换到临时目录
            original_cwd = os.getcwd()
            os.chdir(temp_project)
            
            # 添加临时项目到Python路径
            sys.path.insert(0, str(temp_project))
            
            try:
                # 测试配置管理器在新环境中的行为
                from core.config_manager import ConfigManager
                
                config = ConfigManager()
                project_root = config.get('paths.project_root')
                data_dir = config.get('paths.data_dir')
                
                print(f"新环境项目根目录: {project_root}")
                print(f"新环境数据目录: {data_dir}")
                
                # 检查路径是否指向临时目录
                if str(project_root).startswith(temp_dir):
                    print("✅ 配置管理器在新环境中正确工作")

                    # 检查数据目录是否在新环境中
                    if str(data_dir).startswith(temp_dir):
                        print("✅ 数据目录正确指向新环境")

                        # 检查是否在新环境中创建了正确的目录结构
                        expected_dirs = ['data', 'logs', 'temp', 'backup']
                        all_dirs_correct = True

                        for dir_name in expected_dirs:
                            expected_path = Path(temp_project) / dir_name
                            if expected_path.exists():
                                print(f"✅ 在新环境中创建了 {dir_name} 目录")
                            else:
                                print(f"❌ 新环境中缺少 {dir_name} 目录")
                                all_dirs_correct = False

                        return all_dirs_correct
                    else:
                        print(f"❌ 数据目录仍指向原路径: {data_dir}")
                        return False
                else:
                    print(f"❌ 配置管理器仍然指向原路径: {project_root}")
                    return False
                    
            finally:
                # 恢复原工作目录
                os.chdir(original_cwd)
                # 移除临时路径
                if str(temp_project) in sys.path:
                    sys.path.remove(str(temp_project))
                    
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_portability():
    """测试路径可移植性"""
    print("\n=== 测试路径可移植性 ===")
    
    try:
        from core.config_manager import ConfigManager
        from core.path_resolver import PathResolver
        
        # 获取当前配置
        config = ConfigManager()
        project_root = Path(config.get('paths.project_root'))
        
        # 测试数据库路径
        db_path = PathResolver.get_database_path('test.db')
        db_path_obj = Path(db_path)
        
        print(f"项目根目录: {project_root}")
        print(f"数据库路径: {db_path}")
        
        # 检查数据库路径是否在项目根目录下
        try:
            db_path_obj.relative_to(project_root)
            print("✅ 数据库路径相对于项目根目录")
            return True
        except ValueError:
            print("❌ 数据库路径不在项目根目录下")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试历史区间标签页目录创建行为...")
    
    results = []
    
    # 运行测试
    results.append(("目录创建测试", test_historical_tab_directory_creation()))
    results.append(("路径可移植性测试", test_path_portability()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！历史区间标签页不再创建硬编码目录。")
        print("现在可以安全地将程序复制到其他位置运行。")
    else:
        print("⚠️  部分测试失败，仍可能存在硬编码目录创建问题。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
