#!/usr/bin/env python3
"""
测试历史区间分析图表的图例位置修改
"""

import sys
import os
from pathlib import Path
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_legend_position():
    """测试图例位置修改效果"""
    print("=== 测试历史区间分析图表图例位置 ===")
    
    try:
        # 导入相关模块
        from analysis.historical_chart_visualizer import HistoricalChartVisualizer
        from analysis.interval_visualizer import VisualizationData
        
        # 创建测试数据
        print("创建测试数据...")
        
        # 生成时间序列
        start_time = datetime.now() - timedelta(hours=2)
        timestamps = []
        for i in range(20):
            timestamps.append(start_time + timedelta(minutes=i*6))
        
        # 生成模拟的区间数据
        np.random.seed(42)  # 确保结果可重现
        
        # 主胜区间 (Y坐标)
        home_y_coords = np.random.randint(1, 10, 20).tolist()
        
        # 平局区间 (Y坐标) - 故意让最后几个点在较高位置
        draw_y_coords = np.random.randint(5, 15, 20).tolist()
        draw_y_coords[-3:] = [13, 14, 15]  # 最后三个点在高位置
        
        # 客胜区间 (Y坐标)
        away_y_coords = np.random.randint(1, 8, 20).tolist()
        
        # 创建区间标签映射
        interval_labels = {}
        for i in range(1, 16):
            if i <= 5:
                interval_labels[i] = f"低区间{i}"
            elif i <= 10:
                interval_labels[i] = f"中区间{i}"
            else:
                interval_labels[i] = f"高区间{i}"
        
        # 创建可视化数据对象
        visualization_data = VisualizationData(
            timestamps=[t.strftime('%Y-%m-%d %H:%M:%S') for t in timestamps],
            home_y_coords=home_y_coords,
            draw_y_coords=draw_y_coords,
            away_y_coords=away_y_coords,
            interval_labels=interval_labels
        )
        
        print(f"生成了 {len(timestamps)} 个时间点的测试数据")
        print(f"平局最后三个点的Y坐标: {draw_y_coords[-3:]}")
        
        # 创建图表可视化器
        print("创建图表可视化器...")
        visualizer = HistoricalChartVisualizer()
        
        # 绘制图表
        print("绘制测试图表...")
        success = visualizer.plot_historical_intervals(
            visualization_data, 
            company_name="测试公司"
        )
        
        if success:
            print("✅ 图表绘制成功")
            
            # 保存图表到文件进行检查
            output_file = current_dir / "test_legend_position.png"
            save_success = visualizer.save_chart(str(output_file), dpi=150)
            
            if save_success:
                print(f"✅ 图表已保存到: {output_file}")
                print("请检查图表中的图例是否已移到右侧外部，不再遮挡平局线的最后几个点")
                
                # 检查图例设置
                legend = visualizer.ax.get_legend()
                if legend:
                    bbox = legend.get_bbox_to_anchor()
                    print(f"图例位置设置: bbox_to_anchor={bbox}")
                    
                    if bbox and bbox.x0 > 1.0:
                        print("✅ 图例已正确设置在图表外部")
                        return True
                    else:
                        print("❌ 图例仍在图表内部")
                        return False
                else:
                    print("❌ 未找到图例")
                    return False
            else:
                print("❌ 图表保存失败")
                return False
        else:
            print("❌ 图表绘制失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_layout_adjustment():
    """测试布局调整效果"""
    print("\n=== 测试布局调整效果 ===")
    
    try:
        from analysis.historical_chart_visualizer import HistoricalChartVisualizer
        
        # 创建可视化器
        visualizer = HistoricalChartVisualizer()
        
        # 检查图形的布局设置
        print("检查图形布局设置...")
        
        # 获取子图的位置
        pos = visualizer.ax.get_position()
        print(f"子图位置: x0={pos.x0:.3f}, y0={pos.y0:.3f}, x1={pos.x1:.3f}, y1={pos.y1:.3f}")
        print(f"子图宽度: {pos.width:.3f}")
        
        if pos.x1 <= 0.85:
            print("✅ 子图已为图例留出空间")
            return True
        else:
            print("❌ 子图未为图例留出足够空间")
            return False
            
    except Exception as e:
        print(f"❌ 布局测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试历史区间分析图表图例位置修改...")
    
    results = []
    
    # 运行测试
    results.append(("图例位置测试", test_legend_position()))
    results.append(("布局调整测试", test_layout_adjustment()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！")
        print("✅ 图例已成功移到图表外部")
        print("✅ 不再遮挡平局线的最后几个数据点")
        print("✅ 图表布局已正确调整")
    else:
        print("⚠️  部分测试失败，请检查修改。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
