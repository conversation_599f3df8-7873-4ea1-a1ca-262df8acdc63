#!/usr/bin/env python3
"""
测试路径修复是否有效
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_config_manager():
    """测试配置管理器的路径配置"""
    print("=== 测试配置管理器路径配置 ===")
    
    try:
        from core.config_manager import ConfigManager
        
        # 创建配置管理器实例
        config = ConfigManager()
        
        # 获取路径配置
        project_root = config.get('paths.project_root')
        data_dir = config.get('paths.data_dir')
        logs_dir = config.get('paths.logs_dir')
        temp_dir = config.get('paths.temp_dir')
        backup_dir = config.get('paths.backup_dir')
        
        print(f"项目根目录: {project_root}")
        print(f"数据目录: {data_dir}")
        print(f"日志目录: {logs_dir}")
        print(f"临时目录: {temp_dir}")
        print(f"备份目录: {backup_dir}")
        
        # 检查路径是否可移植（不依赖特定磁盘位置）
        paths_to_check = [
            ("项目根目录", project_root),
            ("数据目录", data_dir),
            ("日志目录", logs_dir),
            ("临时目录", temp_dir),
            ("备份目录", backup_dir)
        ]

        print("\n=== 路径可移植性检查 ===")
        is_portable = True

        for name, path in paths_to_check:
            if path:
                # 检查路径是否基于当前项目结构
                if str(path).startswith(str(project_root)):
                    print(f"✅ {name}: 基于项目根目录 (可移植)")
                else:
                    print(f"❌ {name}: 不基于项目根目录 (不可移植): {path}")
                    is_portable = False

        if is_portable:
            print("✅ 所有路径都是可移植的")
        else:
            print("❌ 存在不可移植的路径配置")
        
        # 检查路径是否存在且可访问
        print("\n=== 检查目录是否存在 ===")
        for name, path in [
            ("项目根目录", project_root),
            ("数据目录", data_dir),
            ("日志目录", logs_dir),
            ("临时目录", temp_dir),
            ("备份目录", backup_dir)
        ]:
            if path and os.path.exists(path):
                print(f"✅ {name}: {path}")
            else:
                print(f"❌ {name}不存在: {path}")
        
        return is_portable
        
    except Exception as e:
        print(f"❌ 测试配置管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_path_resolver():
    """测试路径解析器"""
    print("\n=== 测试路径解析器 ===")
    
    try:
        from core.path_resolver import PathResolver
        
        # 测试数据库路径解析
        db_path = PathResolver.get_database_path('football.db')
        print(f"数据库路径: {db_path}")
        
        # 检查路径是否可移植
        from core.config_manager import ConfigManager
        config = ConfigManager()
        project_root = config.get('paths.project_root')

        if str(db_path).startswith(str(project_root)):
            print("✅ 路径解析器返回可移植路径")
            return True
        else:
            print(f"❌ 路径解析器返回不可移植路径: {db_path}")
            return False
            
    except Exception as e:
        print(f"❌ 测试路径解析器失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_import():
    """测试config.py中的路径配置"""
    print("\n=== 测试config.py路径配置 ===")
    
    try:
        import config
        
        # 检查主要的数据库路径
        paths_to_check = [
            ("DB_MATCHES", getattr(config, 'DB_MATCHES', None)),
            ("DB_TEAMS", getattr(config, 'DB_TEAMS', None)),
            ("DB_ODDS_INTERVALS", getattr(config, 'DB_ODDS_INTERVALS', None)),
            ("DB_GUANGYISHILI", getattr(config, 'DB_GUANGYISHILI', None)),
            ("DATA_DIR", getattr(config, 'DATA_DIR', None)),
        ]
        
        from core.config_manager import ConfigManager
        config = ConfigManager()
        project_root = config.get('paths.project_root')

        is_portable = True
        for name, path in paths_to_check:
            if path:
                print(f"{name}: {path}")
                if str(path).startswith(str(project_root)):
                    print(f"✅ {name} 路径可移植")
                else:
                    print(f"❌ {name} 路径不可移植")
                    is_portable = False

        return is_portable
        
    except Exception as e:
        print(f"❌ 测试config.py失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试路径修复...")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"脚本位置: {__file__}")
    
    results = []
    
    # 运行各项测试
    results.append(("配置管理器", test_config_manager()))
    results.append(("路径解析器", test_path_resolver()))
    results.append(("config.py", test_config_import()))
    
    # 汇总结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "="*50)
    if all_passed:
        print("🎉 所有测试通过！路径硬编码问题已修复。")
    else:
        print("⚠️  部分测试失败，仍存在硬编码路径问题。")
    print("="*50)
    
    return all_passed

if __name__ == "__main__":
    main()
